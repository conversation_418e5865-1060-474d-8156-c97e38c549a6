# -*- coding:utf-8 -*-
import os.path
import sys
import subprocess
from threading import Thread
import socket
import hashlib

import wx.lib.agw.hyperlink as lib_hyperlink
import re
from txt import *
from update import start_update_checker
from fileimei import add_imei_to_queue, sync_queue_to_file,平台选择
from ipc_manager import IPCManager
import time
class Frame(wx.Frame):
    def __init__(self):
        t = 'chuma'
        b = '4.0.7'
        start_update_checker(t, b)
        wx.Frame.__init__(self, None, title=f'{t}--{b}', size=(900, 458), name='frame', style=541072384)
        self.启动窗口 = wx.Panel(self)
        self.Centre()
        self.Bind(wx.EVT_CLOSE, self.启动窗口_将被关闭)
        self.分组单选框2 = wx.RadioBox(self.启动窗口, size=(83, 97), pos=(5, 7), label='出码运营商',
                                       choices=['do', 'au', 'sb'],
                                       majorDimension=0, name='radioBox', style=8)
        self.分组单选框2.SetForegroundColour((0, 0, 255, 255))
        self.分组单选框2.Bind(wx.EVT_RADIOBOX, self.运营商选择_选项被单击)
        self.分组单选框2.SetSelection(1)
        self.列表框2 = wx.ListBox(self.启动窗口, size=(206, 100), pos=(90, 3), name='listBox', style=wx.LB_HSCROLL)
        self.列表框2.SetForegroundColour((0, 128, 255, 255))
        列表框2_字体 = wx.Font(12, 74, 90, 700, False, 'System', 33)
        self.列表框2.SetFont(列表框2_字体)
        self.列表框2.Bind(wx.EVT_LISTBOX, self.列表框2_表项被单击)
        self.展示 = wx.TextCtrl(self.启动窗口, size=(590, 409), pos=(296, 4), value='这里显示状态!', name='text',
                                style=wx.TE_MULTILINE | wx.TE_READONLY | wx.TE_RICH2)
        # self.展示.SetMaxLength(32)
        展示_字体 = wx.Font(10, 74, 90, 400, False, 'Microsoft YaHei UI', 28)
        self.展示.SetFont(展示_字体)
        self.展示.SetForegroundColour((0, 64, 64, 255))
        self.展示.SetOwnBackgroundColour((247, 251, 230, 255))

        # 日志切换按钮 - 放在日志框右上角
        self.日志切换 = wx.Button(self.启动窗口, size=(20, 25), pos=(866, 4), label='◀', name='log_toggle')
        self.日志切换.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        self.日志切换.SetToolTip(wx.ToolTip('收起日志'))
        self.日志切换.Bind(wx.EVT_BUTTON, self.切换日志显示)
        self.日志显示状态 = True

    def 切换日志显示(self, event):
        """切换日志显示状态，同时调整窗口大小"""
        if self.日志显示状态:
            # 收起日志 - 隐藏日志框并缩小窗口
            self.展示.Hide()
            self.日志切换.SetLabel('▶')
            self.日志切换.SetToolTip(wx.ToolTip('展开日志'))
            self.SetSize((310, 458))  # 缩小窗口宽度
            self.日志显示状态 = False
        else:
            # 展开日志 - 恢复窗口大小并显示日志框
            self.SetSize((900, 458))  # 恢复原窗口大小
            self.展示.Show()
            self.日志切换.SetLabel('◀')
            self.日志切换.SetToolTip(wx.ToolTip('收起日志'))
            self.日志显示状态 = True
        # 底部链接区域 - 重新布局避免重叠
        self.进入后台 = lib_hyperlink.HyperLinkCtrl(self.启动窗口, size=(60, 22), pos=(6, 370), name='staticText',
                                                    label='管理后台',
                                                    URL='https://unlockoko.com/admin/')
        self.进入后台.SetToolTip(wx.ToolTip('进入IMEI管理后台'))

        self.do查询 = lib_hyperlink.HyperLinkCtrl(self.启动窗口, size=(53, 22), pos=(6, 390), name='staticText',
                                                  label='DO查询',
                                                  URL='http://nw-restriction.nttdocomo.co.jp/top.php')
        self.do查询.SetToolTip(wx.ToolTip('查询do黑白名单'))
        do查询_字体 = wx.Font(9, 70, 90, 400, True, 'Microsoft YaHei UI', -1)
        self.do查询.SetFont(do查询_字体)
        self.do查询.SetForegroundColour((0, 0, 255, 255))

        self.au查询 = lib_hyperlink.HyperLinkCtrl(self.启动窗口, size=(53, 22), pos=(65, 390), name='staticText',
                                                  label='AU查询',
                                                  URL='https://my.au.com/cmn/WCV009001/WCE009001.hc')
        self.au查询.SetToolTip(wx.ToolTip('查询AU黑白名单'))

        # 底部按钮区域
        self.文件管理 = wx.Button(self.启动窗口, size=(70, 32), pos=(130, 390), label='文件管理', name='button')
        文件管理_字体 = wx.Font(11, 74, 90, 700, False, 'Microsoft YaHei UI', 28)
        self.文件管理.SetFont(文件管理_字体)
        self.文件管理.Bind(wx.EVT_BUTTON, self.文件)

        self.串号比较 = wx.Button(self.启动窗口, size=(70, 32), pos=(210, 390), label='串号比对', name='button')
        串号比较_字体 = wx.Font(11, 74, 90, 700, False, 'Microsoft YaHei UI', 28)
        self.串号比较.SetFont(串号比较_字体)
        self.串号比较.Bind(wx.EVT_BUTTON, self.串号比较_按钮被单击)

        # 设置链接字体样式
        进入后台_字体 = wx.Font(9, 70, 90, 400, True, 'Microsoft YaHei UI', -1)
        self.进入后台.SetFont(进入后台_字体)
        self.进入后台.SetForegroundColour((0, 0, 255, 255))

        au查询_字体 = wx.Font(9, 70, 90, 400, True, 'Microsoft YaHei UI', -1)
        self.au查询.SetFont(au查询_字体)
        self.au查询.SetForegroundColour((0, 0, 255, 255))
        self.打开配置文件 = wx.Button(self.启动窗口, size=(80, 32), pos=(6, 106), label='编辑配置', name='button')
        self.打开配置文件.Bind(wx.EVT_BUTTON, self.打开配置文件_按钮被单击)
        self.处理文本 = wx.Button(self.启动窗口, size=(110, 32), pos=(102, 108), label='串号文本处理', name='button')
        self.处理文本.Bind(wx.EVT_BUTTON, self.处理文本_按钮被单击)
        self.查询imei = wx.Button(self.启动窗口, size=(80, 32), pos=(6, 146), label='查询imei信息', name='button')
        self.查询imei.Bind(wx.EVT_BUTTON, self.查询imei信息被单击)
        self.编辑框imei位置 = wx.TextCtrl(self.启动窗口, size=(210, 27), pos=(3, 224), value='', name='text', style=0)
        self.编辑框imei位置.Bind(wx.EVT_TEXT, self.编辑框imei位置_内容被改变)
        self.选择IMEI位置 = wx.Button(self.启动窗口, size=(80, 32), pos=(214, 222), label='选择imei', name='button')
        self.选择IMEI位置.Bind(wx.EVT_BUTTON, self.选择IMEI位置_按钮被单击)
        self.编辑框保存位置 = wx.TextCtrl(self.启动窗口, size=(209, 28), pos=(3, 263), value='', name='text', style=0)
        self.编辑框保存位置.Bind(wx.EVT_TEXT, self.编辑框保存位置_内容被改变)
        self.保存位置 = wx.Button(self.启动窗口, size=(80, 32), pos=(214, 261), label='保存位置', name='button')
        self.保存位置.Bind(wx.EVT_BUTTON, self.保存位置_按钮被单击)
        self.打开待解IMEI = wx.Button(self.启动窗口, size=(80, 32), pos=(8, 186), label='待解imei查编', name='button')
        self.打开待解IMEI.Bind(wx.EVT_BUTTON, self.打开待解IMEI_按钮被单击)
        self.打开已解IMEI = wx.Button(self.启动窗口, size=(80, 32), pos=(102, 186), label='已解imei查编', name='button')
        self.打开已解IMEI.Bind(wx.EVT_BUTTON, self.打开已解IMEI_按钮被单击)
        self.停止 = wx.Button(self.启动窗口, size=(106, 53), pos=(10, 312), label='停止', name='button')
        self.停止.Hide()
        停止_字体 = wx.Font(14, 74, 90, 700, False, 'Microsoft YaHei UI', 28)
        self.停止.SetFont(停止_字体)
        self.停止.SetForegroundColour((255, 0, 0, 255))
        self.停止.Bind(wx.EVT_BUTTON, self.停止_按钮被单击)
        self.开始 = wx.Button(self.启动窗口, size=(107, 52), pos=(162, 313), label='开始出码', name='button')
        开始_字体 = wx.Font(14, 74, 90, 700, False, 'Microsoft YaHei UI', 28)
        self.开始.SetFont(开始_字体)
        self.开始.SetForegroundColour((0, 128, 0, 255))
        self.开始.Bind(wx.EVT_BUTTON, self.开始_按钮被单击)
        self.程序多开 = wx.Button(self.启动窗口, size=(91, 32), pos=(196, 186), label='多开出码', name='button')
        self.程序多开.Bind(wx.EVT_BUTTON, self.出码多开)
        self.进度条 = wx.Gauge(self.启动窗口, range=100, size=(206, 30), pos=(87, 146), name='gauge',
                               style=wx.GA_HORIZONTAL)
        self.进度条.SetValue(0)
        self.进度条.Hide()
        self.账号 = {}
        self.账号选择 = None
        self.桌面位置 = os.path.join(os.path.expanduser("~"), 'Desktop')
        self.停止变量 = [None, {}]  # 可变
        self.imei列表 = None
        self.t = None
        # 文本处理窗口实例初始化为空
        self.文本处理窗口 = None
        self._instance_socket = None  # 用于保持端口绑定的socket
        self.ipc_manager = None  # IPC管理器
        self.运营商选择('au')

        # 使用socket端口检测替代psutil进程检测
        self.程序路径 = sys.argv[0]  # 完整路径
        self.程序名 = os.path.basename(sys.argv[0])  # 用于显示
        # 使用程序基础名称作为标识符，确保py和exe使用相同端口范围
        program_identifier = os.path.splitext(os.path.basename(self.程序路径))[0]
        count = self._detect_instances(program_identifier)
        if count > 2:
            if len(sys.argv) > 1:
                n = str(int(count / 2))
                self.imei位置 = f'imei-{n}.txt'
                self.imei保存位置 = f'unlockcode-{n}.txt'
                self.编辑框保存位置.write(self.imei保存位置)
                self.编辑框imei位置.write(self.imei位置)

                # 检查是否有自定义窗口名称
                if len(sys.argv) > 2:
                    custom_name = sys.argv[2]
                    new_title = f'出码窗口-{custom_name}'
                else:
                    new_title = f'出码窗口-多开-{n}'
                self.SetTitle(new_title)
                # 设置多开窗口位置偏移，避免重叠
                instance_num = int(n)
                offset_step = 35  # 每个窗口偏移35像素

                # 计算新位置
                current_pos = self.GetPosition()
                base_offset = (instance_num - 1) * offset_step
                new_x = current_pos.x + base_offset
                new_y = current_pos.y + base_offset

                # 确保窗口不会超出屏幕边界
                display_size = wx.GetDisplaySize()
                window_size = self.GetSize()

                # 如果超出右边界，换行显示
                if new_x + window_size.width > display_size.width:
                    new_x = 50 + ((instance_num - 1) % 3) * 200  # 每行最多3个窗口
                    new_y = current_pos.y + ((instance_num - 1) // 3) * 100

                # 如果超出下边界，重置到顶部
                if new_y + window_size.height > display_size.height:
                    new_y = 50

                self.SetPosition((new_x, new_y))
                self.多开更新能解列表()
                if not os.path.exists(self.imei位置):
                    with open(self.imei位置, 'w') as f:
                        f.close()
            else:
                wx.MessageBox(f'{self.程序名}\n为了稳定性\n仅支持程序中点击多开按钮进行多开', caption='多开提示')
                wx.Exit()
                sys.exit()
        else:
            self.imei位置 = 'imei.txt'
            if not os.path.exists(self.imei位置):
                with open(self.imei位置, 'w', encoding='utf-8') as f:
                    f.close()
            self.imei保存位置 = 'unlockcode.txt'
            self.编辑框保存位置.write(self.imei保存位置)
            self.编辑框imei位置.write(self.imei位置)

        # 初始化IPC管理器
        self._init_ipc_manager()

    def _get_base_port(self, program_name):
        """根据程序名生成基础端口号"""
        hash_obj = hashlib.md5(program_name.encode('utf-8'))
        port = 50000 + (int(hash_obj.hexdigest()[:4], 16) % 10000)
        return port

    def _detect_instances(self, program_name):
        """检测程序实例数量并绑定端口"""
        base_port = self._get_base_port(program_name)
        instance_count = 0

        # 尝试绑定端口，找到可用端口并计算实例数
        for i in range(10):  # 最多支持10个实例
            port = base_port + i
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.bind(('127.0.0.1', port))
                sock.listen(1)
                # 绑定成功，说明这是第i+1个实例
                self._instance_socket = sock
                instance_count = i + 1
                break
            except OSError:
                # 端口被占用，继续尝试下一个
                try:
                    sock.close()
                except:
                    pass
                continue
            except Exception:
                # 其他异常，继续尝试下一个端口
                try:
                    sock.close()
                except:
                    pass
                continue

        # 如果所有端口都被占用，返回默认值
        if instance_count == 0:
            instance_count = 1

        return instance_count * 2

    def _init_ipc_manager(self):
        """初始化IPC管理器"""
        try:
            # 使用当前绑定的端口+1000作为IPC端口，避免冲突
            if hasattr(self, '_instance_socket') and self._instance_socket:
                ipc_port = self._instance_socket.getsockname()[1] + 1000
            else:
                ipc_port = 51000  # 默认端口

            instance_id = f"{self.程序名}_{ipc_port}"
            self.ipc_manager = IPCManager(instance_id, self.Title, ipc_port, self)
        except Exception as e:
            print(f"初始化IPC管理器失败: {e}")
            self.ipc_manager = None

    def 处理文本_按钮被单击(self, event):
        if self.文本处理窗口 is None:
            # 创建并显示文本处理窗口
            self.文本处理窗口 = 文本处理窗口(self,
                                             title=self.Title)

            # 设置IPC管理器的文本窗口引用
            if self.ipc_manager:
                self.ipc_manager.set_text_window(self.文本处理窗口)
                self.ipc_manager.update_text_window_status(True)

            # 智能计算文本处理窗口位置，避免重叠
            self._position_text_window()
        else:
            # 如果窗口已经存在，直接显示它
            self.文本处理窗口.Raise()

    def _position_text_window(self):
        """智能定位文本处理窗口位置"""
        # 获取屏幕和窗口尺寸
        screen_width, screen_height = wx.GetDisplaySize()
        main_x, main_y = self.GetPosition()
        main_width, main_height = self.GetSize()
        text_width, text_height = self.文本处理窗口.GetSize()

        # 优先尝试在主窗口右侧放置
        new_x = main_x + main_width + 10  # 10像素间距
        new_y = main_y

        # 检查右侧是否有足够空间
        if new_x + text_width > screen_width:
            # 右侧空间不足，尝试左侧
            new_x = main_x - text_width - 10
            if new_x < 0:
                # 左侧也不足，放置在屏幕右侧
                new_x = screen_width - text_width - 50
                new_y = main_y + 50  # 稍微偏移避免完全重叠

        # 确保窗口不会超出屏幕下边界
        if new_y + text_height > screen_height:
            new_y = screen_height - text_height - 50

        # 确保窗口不会超出屏幕上边界
        if new_y < 0:
            new_y = 50

        self.文本处理窗口.SetPosition((new_x, new_y))

    def 查询imei信息被单击(self, event):
        def is_valid_imei(imei):
            # IMEI号通常是15位数字
            return bool(re.match(r"^\d{15}$", imei))

        dialog = wx.TextEntryDialog(self, "请输入需要查询的IMEI号:", "IMEI查询详情", "")
        if dialog.ShowModal() == wx.ID_OK:
            imei = dialog.GetValue().strip()
            # 判断IMEI号是否有效
            if is_valid_imei(imei):
                型号数据 = 型号数据csv()
                数据 = 型号数据.read_data()
                if s := 数据.get(imei[:8], False):
                    dialog = wx.MessageDialog(
                        self,
                        f"查询到数据，是否联网查询详细？\n{imei}:\n{str(s)}",
                        "查询确认",
                        wx.YES_NO | wx.ICON_QUESTION
                    )

                    # 获取用户选择
                    result = dialog.ShowModal()

                    # 根据用户选择执行操作
                    if result == wx.ID_YES:
                        查询imei信息(imei, self.展示文本)
                    else:
                        self.展示文本(f'imei:{imei} ' + str(s))
                        return
                else:
                    查询imei信息(imei, self.展示文本)

            else:
                wx.MessageBox("IMEI号格式不正确，请重新输入!", "错误", wx.OK | wx.ICON_ERROR)
        dialog.Destroy()

    def 编辑框imei位置_内容被改变(self, event):
        self.imei位置 = self.编辑框imei位置.GetValue()
        print(self.imei位置)

    def 编辑框保存位置_内容被改变(self, event):
        self.imei保存位置 = self.编辑框保存位置.GetValue()
        print(self.imei保存位置)

    def 多开更新能解列表(self):
        self.分组单选框2.Destroy()  # 销毁旧的 RadioBox
        self.分组单选框2 = wx.RadioBox(self.启动窗口, size=(83, 97), pos=(5, 7), label='出码运营商',
                                       choices=['au', 'sb'],
                                       majorDimension=0, name='radioBox', style=8)
        self.分组单选框2.SetForegroundColour((0, 0, 255, 255))
        self.分组单选框2.Bind(wx.EVT_RADIOBOX, self.运营商选择_选项被单击)
        self.分组单选框2.SetSelection(1)

    def 创建带行号文本框(self, parent, title, size=None, readonly=False):
        """创建支持彩色的STC编辑器"""
        sizer = wx.BoxSizer(wx.VERTICAL)

        # 标题
        title_label = wx.StaticText(parent, label=title)
        title_label.SetFont(wx.Font(10, wx.FONTFAMILY_DEFAULT,
                                    wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD))
        sizer.Add(title_label, 0, wx.ALL, 5)

        # STC控件
        editor = stc.StyledTextCtrl(parent)
        editor.SetMarginType(0, stc.STC_MARGIN_NUMBER)
        editor.SetMarginWidth(0, 40)
        editor.StyleSetBackground(stc.STC_STYLE_LINENUMBER, wx.Colour(240, 240, 240))
        editor.StyleSetForeground(stc.STC_STYLE_LINENUMBER, wx.Colour(100, 100, 100))

        if readonly:
            editor.SetReadOnly(True)
            # 定义彩色样式
            editor.StyleSetForeground(1, wx.Colour(255, 0, 0))  # 红色：差异标识
            editor.StyleSetForeground(2, wx.Colour(0, 100, 0))  # 绿色：原始内容
            editor.StyleSetForeground(3, wx.Colour(0, 0, 150))  # 蓝色：行号

        if size:
            editor.SetSize(size)

        sizer.Add(editor, 1, wx.EXPAND)
        return sizer

    def 串号比较_按钮被单击(self, event):
        """优化布局的串号比对工具"""
        dlg = wx.Dialog(self, title="串号比对工具", size=(1000, 700))
        panel = wx.Panel(dlg)
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # 输入区域（长方形布局）
        input_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # 左侧输入框Sizer
        left_sizer = self.创建带行号文本框(panel, "原始数据(出码前IMEI-直接粘贴)", size=(450, 300))
        # 右侧输入框Sizer
        right_sizer = self.创建带行号文本框(panel, "比对数据(出码后解锁码-直接粘贴)", size=(450, 300))

        input_sizer.Add(left_sizer, 1, wx.EXPAND | wx.ALL, 5)
        input_sizer.Add(right_sizer, 1, wx.EXPAND | wx.ALL, 5)

        # 结果区域Sizer
        result_sizer = self.创建带行号文本框(panel, "比对结果", size=(-1, 150), readonly=True)

        # 操作按钮Sizer
        btn_sizer = wx.BoxSizer(wx.HORIZONTAL)
        compare_btn = wx.Button(panel, label="开始比对", size=(120, 40))
        原始排序 = wx.Button(panel, label="原始排序", size=(120, 40))
        close_btn = wx.Button(panel, label="关闭", size=(120, 40))
        btn_sizer.Add(compare_btn, 0, wx.RIGHT, 20)
        btn_sizer.Add(原始排序, 0, wx.RIGHT, 20)
        btn_sizer.Add(close_btn, 0)

        # 布局比例：输入区2/3，结果区1/3
        main_sizer.Add(input_sizer, 2, wx.EXPAND)
        main_sizer.Add(result_sizer, 1, wx.EXPAND | wx.ALL, 5)
        main_sizer.Add(btn_sizer, 0, wx.ALIGN_CENTER | wx.BOTTOM, 10)
        panel.SetSizer(main_sizer)

        # 事件绑定
        compare_btn.Bind(wx.EVT_BUTTON, lambda e: self.执行比对(
            left_sizer.GetChildren()[1].GetWindow(),  # 获取STC控件
            right_sizer.GetChildren()[1].GetWindow(),
            result_sizer.GetChildren()[1].GetWindow()
        ))
        原始排序.Bind(wx.EVT_BUTTON, lambda e: self.执行原始排序(
            left_sizer.GetChildren()[1].GetWindow(),  # 原始数据控件
            right_sizer.GetChildren()[1].GetWindow(),  # 比对数据控件
            result_sizer.GetChildren()[1].GetWindow()  # 结果输出控件
        ))

        close_btn.Bind(wx.EVT_BUTTON, lambda e: dlg.Destroy())
        # 相对于父窗口居中，避免与多开窗口重叠
        dlg.CenterOnParent()
        dlg.Show()

    def 执行原始排序(self, src_ctrl, dst_ctrl, result_ctrl):
        try:
            # 获取原始数据和比对数据
            原始数据 = [line.strip() for line in src_ctrl.GetText().split('\n') if line.strip()]
            比对数据 = [line.strip() for line in dst_ctrl.GetText().split('\n') if line.strip()]

            # 构建比对数据字典：key=imei前15位，value=后面内容
            dst_dict = {}
            比对数据未查找到的 = []
            for line in 比对数据:
                match = re.search(r'(\d{15})\s*(.*)', line)  # 匹配 IMEI 及其后内容
                if match:
                    imei = match.group(1)
                    value = match.group(2).strip()
                    dst_dict[imei] = value
                else:
                    比对数据未查找到的.append(line)
                    print(f"未在该行找到有效IMEI：{line}")

            # 处理原始数据，进行匹配并生成结果
            result = []
            for line in 原始数据:
                match = re.search(r'(\d{15})', line)  # 提取原始行中的 IMEI
                if not match:
                    result.append(line)
                    continue
                imei = match.group(1)
                matched = dst_dict.get(imei, '')
                result.append(f"{line} {matched}")  # 拼接结果

            # 显示结果到比对结果框（只读）
            result_ctrl.SetReadOnly(False)
            result.extend(比对数据未查找到的)
            result_ctrl.SetText('\n'.join(result))
            # result_ctrl.SetReadOnly(True)

        except Exception as e:
            wx.MessageBox(f"原始排序失败：{str(e)}", "错误", wx.OK | wx.ICON_ERROR)

    def 执行比对(self, src_ctrl, dst_ctrl, result_ctrl):
        """支持双向来源标注的比对逻辑"""
        try:
            # 清空结果窗口
            result_ctrl.SetReadOnly(False)
            result_ctrl.SetText('')

            # 收集原始数据（来源窗口1）
            src_data = []
            for idx, line in enumerate(src_ctrl.GetText().split('\n'), 1):
                stripped_line = line.strip()
                if stripped_line:
                    key = stripped_line[:15]
                    src_data.append(("原始窗口", key, idx, stripped_line))  # 添加来源标识

            # 收集比对数据（来源窗口2）
            dst_data = []
            for idx, line in enumerate(dst_ctrl.GetText().split('\n'), 1):
                stripped_line = line.strip()
                if stripped_line:
                    key = stripped_line[:15]
                    dst_data.append(("比对窗口", key, idx, stripped_line))  # 添加来源标识

            # 提取键集合
            src_keys = {item[1] for item in src_data}
            dst_keys = {item[1] for item in dst_data}

            # 查找双向差异
            diff_records = []
            # 原始窗口特有项（红色）
            diff_records.extend([(source, key, line, content)
                                 for (source, key, line, content) in src_data
                                 if key not in dst_keys])
            # 比对窗口特有项（紫色）
            diff_records.extend([(source, key, line, content)
                                 for (source, key, line, content) in dst_data
                                 if key not in src_keys])

            # 添加标题
            result_ctrl.AddText("差异详情（红色-原始窗口差异，紫色-比对窗口差异）：\n")
            result_ctrl.StartStyling(result_ctrl.GetLength())
            result_ctrl.SetStyling(result_ctrl.GetLength(), 1)  # 标题用红色

            # 定义颜色样式
            COLOR_SRC = 1  # 原始窗口红色
            COLOR_DST = 4  # 比对窗口紫色
            result_ctrl.StyleSetForeground(COLOR_DST, wx.Colour(128, 0, 128))  # 新增紫色样式

            total_src_diff = 0
            total_dst_diff = 0
            for source, key, line_num, content in sorted(diff_records, key=lambda x: x[1]):
                # 统计不同来源
                if source == "原始窗口":
                    total_src_diff += 1
                    style = COLOR_SRC
                    source_note = "（来自原始数据）"
                else:
                    total_dst_diff += 1
                    style = COLOR_DST
                    source_note = "（来自比对数据）"

                # 添加差异项
                header = f"\n▶ 差异项 {total_src_diff + total_dst_diff} [ {key} ] {source_note}\n"
                result_ctrl.AddText(header)
                # 设置标题颜色
                start_pos = result_ctrl.GetLength() - len(header)
                result_ctrl.StartStyling(start_pos)
                result_ctrl.SetStyling(len(header), style)

                # 添加详细信息
                line_info = f"    ▪ 行号：{line_num}  内容：{content}\n"
                result_ctrl.AddText(line_info)

                # 设置行号（蓝色）和内容（绿色）
                start_pos = result_ctrl.GetLength() - len(line_info)
                # 行号部分
                result_ctrl.StartStyling(start_pos + 7)  # 定位到行号位置
                result_ctrl.SetStyling(len(str(line_num)), 3)
                # 内容部分
                result_ctrl.StartStyling(start_pos + 7 + len(str(line_num)) + 2)
                result_ctrl.SetStyling(len(content), 2)

            # 统计信息
            stats = (f"\n★ 差异统计："
                     f"\n原始窗口特有项：{total_src_diff} 处"
                     f"\n比对窗口特有项：{total_dst_diff} 处")
            result_ctrl.AddText(stats)
            # 设置统计颜色
            start_pos = result_ctrl.GetLength() - len(stats)
            result_ctrl.StartStyling(start_pos + 6)  # 定位到数字位置
            result_ctrl.SetStyling(len(str(total_src_diff)), COLOR_SRC)
            result_ctrl.StartStyling(start_pos + 20 + len(str(total_src_diff)))
            result_ctrl.SetStyling(len(str(total_dst_diff)), COLOR_DST)

            result_ctrl.SetReadOnly(True)
            result_ctrl.ScrollToLine(0)

        except Exception as e:
            wx.MessageBox(f"比对失败：{str(e)}", "错误", wx.OK | wx.ICON_ERROR)

    def 文件(self, event):
        if os.path.exists('tool\\matepath.exe'):
            os.popen(fr'tool\matepath.exe {os.getcwd()}')
        else:
            os.popen('explorer ' + os.getcwd())

    def 启动窗口_将被关闭(self, event):
        # 清理IPC资源
        if hasattr(self, 'ipc_manager') and self.ipc_manager:
            try:
                self.ipc_manager.cleanup()
            except:
                pass

        # 清理socket资源
        if hasattr(self, '_instance_socket') and self._instance_socket:
            try:
                self._instance_socket.close()
                self._instance_socket = None
            except:
                pass

        if self.t == None:
            wx.Exit()
        elif self.t.is_alive():
            resp = wx.MessageBox(f'{self.t.getName()}正在出码,确定退出吗?', '温馨提示',
                                 wx.OK | wx.CANCEL | wx.ICON_WARNING)
            if resp == wx.OK:
                wx.Exit()
            else:
                self.展示文本('未执行退出')
        elif self.停止变量[1]:
            list = [i for i in self.停止变量[1].keys()]
            resp = wx.MessageBox(f'{list}\n已存在登录信息,确定退出吗', '温馨提示',
                                 wx.OK | wx.CANCEL | wx.ICON_WARNING)
            if resp == wx.OK:
                wx.Exit()
            else:
                self.展示文本('未执行退出')
        else:
            wx.Exit()

    def 运营商选择(self, 运营商):
        info = self.get_account(运营商)
        self.列表框2.Show()
        self.列表框2.Clear()
        self.开始.Hide()

        if 运营商 == 'au':
            if len(info) == 0:
                self.账号选择 = None
                self.开始.Hide()
                self.展示文本(f'{运营商}的账号为空,请先点击编辑账号设定账号', 1)
                self.展示文本(f'点击编辑账号,按要求填写,逗号隔开', 1)
            elif len(info) == 1:
                self.开始.Show()
                self.开始.LabelText = 运营商 + '开始出码'
                self.展示文本(f'你选择了{运营商}出码.准备好后点开始')
                self.列表框2.Append(info[0][0])
                self.账号[info[0][0]] = info[0]
                self.账号选择 = info[0][0]
            else:
                self.账号选择 = None
                self.展示文本(f'你选择了{运营商},具有多个账号,请先选择其中一个', 1)
                self.开始.Show()
                self.开始.LabelText = 运营商 + '开始出码'
                for i in info:
                    self.列表框2.Append(i[0])
                    self.账号[i[0]] = i
        elif 运营商 == 'do':
            if 平台选择 == '电脑':
                self.账号选择 = 'do'
                self.账号[self.账号选择] = '不需要账号'
                self.列表框2.Hide()
                self.展示文本('你选择了DO电脑出码.准备好后点开始')
                self.开始.Show()
                self.开始.LabelText = 运营商 + '开始出码'
                return
            # self.账号选择 = 'do'
            # self.账号[self.账号选择] = '不需要账号'
            device_id = get_device_ids()
            if device_id:
                if len(device_id) == 0:
                    self.账号选择 = None
                    self.开始.Hide()
                    self.展示文本(f'{运营商}的设备为空，请插入登录账号的do设备\n再重新点击{运营商}按钮', 1)
                elif len(device_id) > 1:
                    self.账号选择 = None
                    self.展示文本(f'你选择了{运营商},连接了多台设备,请先选择其中一个', 1)
                    self.开始.Show()
                    self.开始.LabelText = 运营商 + '开始出码'
                    for i in device_id:
                        self.列表框2.Append(i)
                        self.账号[i] = i
                else:
                    self.开始.Show()
                    self.开始.LabelText = 运营商 + '开始出码'
                    self.展示文本(f'你选择了{运营商}出码.准备好后点开始')
                    self.列表框2.Append(device_id[0])
                    self.账号[device_id[0]] = device_id[0]
                    self.账号选择 = device_id[0]
            else:
                self.账号选择 = None
                self.展示文本('请先连接好登录账号的设备后并进行选择后开始', 1)
                # self.分组单选框2.SetSelection(2)
                # self.运营商选择('au')
                self.开始.Show()
                self.开始.LabelText = 运营商 + '开始出码'

    def 展示文本(self, text, color=0):
        if color == 1:
            self.展示.SetDefaultStyle(wx.TextAttr("RED"))
        elif color == 0:
            self.展示.SetDefaultStyle(wx.TextAttr("BLUE"))
        self.展示.write('\n' + time.strftime("%H:%M:%S - ") + str(text))

    def 运营商选择_选项被单击(self, event):
        self.开始.Hide()
        self.账号选择 = None
        # wx.Frame.SetSize(self, 800, 458)
        # wx.TextCtrl.SetSize(self.展示, 750, 409)
        运营商 = self.分组单选框2.GetString(self.分组单选框2.GetSelection())  # 获取到选择的运营商名称
        if 运营商 == 'sb':
            # wx.Frame.SetSize(self, 910, 458)
            # wx.TextCtrl.SetSize(self.展示, 596, 409)
            self.账号选择 = 'sb'
            self.账号[self.账号选择] = '不需要账号'
            self.列表框2.Hide()
            self.展示文本('你选择了SB出码.准备好后点开始')
            self.开始.Show()
            self.开始.LabelText = 运营商 + '开始出码'
        else:
            self.运营商选择(运营商)
        # self.列表框2.Append(str(guiFunction.选择运营商(info)))

    def 列表框2_表项被单击(self, event):
        self.账号选择 = None
        列表选中位置 = self.列表框2.GetSelection()
        infolist = self.列表框2.GetString(列表选中位置)
        self.展示文本(f'您选择了{infolist}')
        self.账号选择 = infolist

    def 打开配置文件_按钮被单击(self, event):

        try:
            if os.path.exists('tool\\Notepad4.exe'):
                os.popen(
                    f'tool\\Notepad4.exe /e gbk /r /t "{os.path.basename(conf_path)}-解锁配置文件-若编辑了配置文件,需要重启软件生效!" {conf_path} ')
            else:
                os.popen(f'notepad {conf_path}')
        except:
            self.展示文本('解锁配置.ini文件打开失败', 1)

    def 选择IMEI位置_按钮被单击(self, event):
        if w := wx.FileSelector('解锁的imei文件', self.桌面位置, '请选择要解锁的imei文档', wildcard='*.txt'):
            self.编辑框imei位置.Clear()
            self.imei位置 = w
            self.编辑框imei位置.write(self.imei位置)

    def 保存位置_按钮被单击(self, event):
        if w := wx.SaveFileSelector('保存在哪里', 'txt', 'unlockcode'):
            self.编辑框保存位置.Clear()
            self.imei保存位置 = w
            self.编辑框保存位置.write(self.imei保存位置)

    def 打开待解IMEI_按钮被单击(self, event):
        if not os.path.exists(self.imei位置):
            with open(self.imei位置, 'w', encoding='utf-8') as f:
                f.close()

        # 检查是否正在解锁中
        if self.停止变量[0] == 0:  # 解锁进行中
            self.显示动态插入对话框()
        else:  # 解锁未进行，保持原有行为
            if os.path.exists(r'tool\Notepad4.exe'):
                os.popen(
                    fr'tool\Notepad4.exe /e utf8 /r /t "{os.path.basename(self.imei位置)}-待解imei查编" {self.imei位置} ')
            else:
                os.popen('notepad ' + self.imei位置)

    def 显示动态插入对话框(self):
        """显示动态插入IMEI的对话框"""
        dlg = wx.Dialog(self, title="解锁进行中 - 动态管理IMEI", size=(500, 400))
        panel = wx.Panel(dlg)
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # 说明文字
        info_text = wx.StaticText(panel, label="解锁正在进行中，您可以：")
        info_font = wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
        info_text.SetFont(info_font)
        main_sizer.Add(info_text, 0, wx.ALL, 10)

        # 按钮区域
        btn_sizer = wx.BoxSizer(wx.HORIZONTAL)

        front_btn = wx.Button(panel, label="前面插入(紧急)", size=(120, 40))
        front_btn.SetBackgroundColour(wx.Colour(255, 200, 200))  # 浅红色
        back_btn = wx.Button(panel, label="后面插入(排队)", size=(120, 40))
        back_btn.SetBackgroundColour(wx.Colour(200, 255, 200))  # 浅绿色
        view_btn = wx.Button(panel, label="查看文件", size=(120, 40))
        view_btn.SetBackgroundColour(wx.Colour(200, 200, 255))  # 浅蓝色

        btn_sizer.Add(front_btn, 0, wx.RIGHT, 10)
        btn_sizer.Add(back_btn, 0, wx.RIGHT, 10)
        btn_sizer.Add(view_btn, 0)
        main_sizer.Add(btn_sizer, 0, wx.ALIGN_CENTER | wx.ALL, 10)

        # 输入区域
        input_label = wx.StaticText(panel, label="输入IMEI (每行一个，支持多行):")
        main_sizer.Add(input_label, 0, wx.LEFT | wx.RIGHT | wx.TOP, 10)

        input_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE, size=(-1, 200))
        input_text.SetHint("请输入15位IMEI号码，每行一个\n例如：\n123456789012345\n987654321098765")
        main_sizer.Add(input_text, 1, wx.EXPAND | wx.ALL, 10)

        # 状态显示
        status_text = wx.StaticText(panel, label="")
        main_sizer.Add(status_text, 0, wx.ALL, 10)

        # 关闭按钮
        close_btn = wx.Button(panel, wx.ID_CLOSE, "关闭")
        main_sizer.Add(close_btn, 0, wx.ALIGN_CENTER | wx.ALL, 10)

        panel.SetSizer(main_sizer)

        # 事件绑定
        front_btn.Bind(wx.EVT_BUTTON, lambda e: self.处理动态插入(input_text, status_text, 'front', dlg))
        back_btn.Bind(wx.EVT_BUTTON, lambda e: self.处理动态插入(input_text, status_text, 'back', dlg))
        view_btn.Bind(wx.EVT_BUTTON, lambda e: self.查看待解文件())
        close_btn.Bind(wx.EVT_BUTTON, lambda e: dlg.Destroy())

        dlg.Centre()
        dlg.ShowModal()
        dlg.Destroy()

    def 处理动态插入(self, input_text, status_text, position, dialog):
        """处理动态插入IMEI的逻辑"""
        content = input_text.GetValue().strip()
        if not content:
            status_text.SetLabel("请输入IMEI号码")
            status_text.SetForegroundColour(wx.Colour(255, 0, 0))
            return

        # 检查是否有活动的解锁线程和队列
        if not hasattr(self, 't') or not self.t or not self.t.is_alive():
            status_text.SetLabel("解锁未在进行中")
            status_text.SetForegroundColour(wx.Colour(255, 0, 0))
            return

        # 获取当前解锁信息中的队列
        if not hasattr(self, 'current_unlock_info') or 'imei队列' not in self.current_unlock_info:
            status_text.SetLabel("无法获取当前解锁队列")
            status_text.SetForegroundColour(wx.Colour(255, 0, 0))
            return

        queue = self.current_unlock_info['imei队列']
        queue_lock = self.current_unlock_info['队列锁']

        # 解析输入的IMEI
        imei_lines = [line.strip() for line in content.split('\n') if line.strip()]

        # 添加到队列
        added_count = add_imei_to_queue(queue, imei_lines, position, queue_lock)

        if added_count > 0:
            # 同步到文件
            with queue_lock:
                sync_queue_to_file(queue, self.imei位置)
                # 新的总数 = 当前队列长度 + 已处理数量
                processed_count = self.current_unlock_info.get('processed_count', 0)
                new_total = len(queue) + processed_count

            # 更新进度条范围
            wx.CallAfter(self.进度条.SetRange, new_total)

            position_text = "队列前端" if position == 'front' else "队列后端"
            status_text.SetLabel(f"成功添加 {added_count} 个IMEI到{position_text}")
            status_text.SetForegroundColour(wx.Colour(0, 128, 0))

            # 清空输入框
            input_text.SetValue("")

            # 更新主界面显示
            wx.CallAfter(self.展示文本, f"动态添加{added_count}个IMEI到{position_text}")
        else:
            status_text.SetLabel("没有有效的IMEI被添加（可能重复或格式错误）")
            status_text.SetForegroundColour(wx.Colour(255, 165, 0))

    def 查看待解文件(self):
        """查看待解文件的只读版本"""
        if os.path.exists(r'tool\Notepad4.exe'):
            os.popen(
                fr'tool\Notepad4.exe /e utf8 /r /ro /l /t "{os.path.basename(self.imei位置)}-待解imei查询-解锁进行中-只读模式" {self.imei位置} ')
        else:
            wx.MessageBox(
                '解锁进行中，文件为只读模式\n请不要进行编辑操作',
                caption='解锁中查看待解imei提示')
            os.popen('notepad ' + self.imei位置)

    def 打开已解IMEI_按钮被单击(self, event):
        if not os.path.exists(self.imei保存位置):
            with open(self.imei保存位置, 'w', encoding='utf-8') as f:
                f.close()
        if os.path.exists(r'tool\Notepad4.exe'):
            os.popen(
                fr'tool\Notepad4.exe /e utf8 /r /l /t "{os.path.basename(self.imei保存位置)}-当前为解锁码" {self.imei保存位置} ')
        else:
            os.popen('notepad ' + self.imei保存位置)
        self.展示文本('解锁码文档已打开')

    def get_account(self, choose):
        try:
            au = eval(config.get('au-账号信息', '登录账号,登录密码,邮箱,邮箱密码'))
            do = eval(config.get('do-账号信息', '登录账号,登录密码,邮箱,邮箱密码'))
        except:
            self.展示文本('账号配置文件出错.请联系管理员', 1)
        info = {'au': au, 'do': do}
        return info[choose]

    def 停止_按钮被单击(self, event):
        self.停止变量[0] = 1
        self.停止.Disable()
        self.展示文本('停止会有延迟,请等待几秒.....', 1)
        t = Thread(target=self.启用按钮, args=(0,))
        t.start()

    def 禁用按钮(self):
        self.开始.Disable()
        self.停止.Show()
        self.停止.Enable()
        self.选择IMEI位置.Disable()
        self.编辑框保存位置.Disable()
        self.列表框2.Disable()
        self.分组单选框2.Disable()
        self.编辑框imei位置.Disable()
        self.进度条.Show()
        self.保存位置.Disable()
        self.展示文本('++++++开始出码++++++')

    def 处理异常IMEI(self, imeierror):
        """处理异常IMEI"""
        if imeierror and len(imeierror) > 0:
            # 弹出异常IMEI处理对话框
            wx.CallAfter(self.显示异常IMEI对话框, imeierror)

    def 显示异常IMEI对话框(self, imeierror):
        """显示异常IMEI处理对话框"""
        # 先保存异常IMEI到文件（无论选择什么都要保存）
        try:
            with open(self.imei位置, 'a', encoding='utf-8') as f:
                for imei in imeierror:
                    f.write('\n' + imei)
        except Exception as e:
            self.展示文本(f'保存异常IMEI失败: {e}', 1)
            # 不需要启用按钮，因为info['停止']()会处理
            return

        imei_list = '\n'.join(imeierror)
        message = f"发现 {len(imeierror)} 个异常跳过的IMEI：\n\n{imei_list}\n\n已保存到待解文件，是否立即重新解锁？"

        dlg = wx.MessageDialog(
            self,
            message,
            "发现异常跳过的IMEI",
            wx.YES_NO | wx.ICON_QUESTION
        )
        dlg.SetYesNoLabels("立即重试", "稍后处理")

        result = dlg.ShowModal()
        dlg.Destroy()

        if result == wx.ID_YES:
            # 立即重试：直接启动解锁流程
            self.展示文本(f'开始重新解锁 {len(imeierror)} 个异常IMEI', 1)
            # 延迟启动解锁，避免界面冲突
            # 注意：重试时会使用相同的运营商和账号，"上次"逻辑会正确处理
            wx.CallLater(500, self.开始_按钮被单击, None)
        else:
            # 稍后处理：不需要再次启用按钮，因为info['停止']()已经启用了
            self.展示文本(f'已保存 {len(imeierror)} 个异常IMEI到待解文件', 1)

    def 启用按钮(self, stop=1):
        if stop == 0:
            self.展示文本('正在停止...')
            start = time.time()
            while self.t.is_alive():
                time.sleep(1)
                print(f'已等待{int(time.time() - start)}秒...正在停止-请耐心')
                if time.time() - start > 30:
                    try:
                        if driver:
                            print('浏览器停止')
                            driver.quit()
                        print('时间太长-强行停止')
                        self.展示文本('时间太长-强行停止', 1)
                        stop_thread(self.t)
                    except:
                        pass
                    finally:
                        break
            print('已停止')
            self.展示文本('已停止')

        # 清理解锁信息
        if hasattr(self, 'current_unlock_info'):
            self.current_unlock_info = None

        self.停止变量[0] = 1
        self.停止.Disable()
        self.开始.Enable()
        self.选择IMEI位置.Enable()
        self.编辑框保存位置.Enable()
        self.列表框2.Enable()
        self.分组单选框2.Enable()
        self.编辑框imei位置.Enable()
        self.进度条.Hide()
        self.保存位置.Enable()
        if 解锁码标识 == '是':
            try:
                with open(self.imei保存位置, 'r', encoding='utf-8', errors='ignore') as 解锁码文件:
                    解锁码 = [i for i in 解锁码文件.readlines() if i.strip()][-1]
                    if '------' not in 解锁码:
                        with open(self.imei保存位置, 'a', encoding='utf-8', errors='ignore') as f:
                            f.write('\n' + time.strftime("%m.%d-%H:%M:%S").center(60, '-') + '\n')
            except:
                pass

    def 开始_按钮被单击(self, event):

        self.进度条.SetValue(0)
        if not os.path.exists(self.imei位置):
            try:
                with open(self.imei位置, 'w', encoding='utf-8') as f:
                    f.close()
            except:
                self.展示文本('请先选择待解imei相关后再开始', 1)
                return
        self.imei列表 = get_imei(self.imei位置)
        num = len(self.imei列表)  # imei数量
        账号 = self.账号.get(self.账号选择)
        运营商 = self.开始.LabelText[:2]  # 得到运营商名称
        if 账号 == None:
            self.展示文本('请先选择相关后再开始', 1)
            if 运营商 == 'do':
                self.运营商选择('do')
        elif self.imei位置 == '':
            self.展示文本('请先选择要解锁的IMEI文件', 1)
        elif self.imei保存位置 == '':
            self.展示文本('请先选择解好后保存在哪里', 1)
        elif num == 0:
            self.展示文本(f'{self.imei位置}-文档为空,请点击查看imei进行补充后再开始', 1)
        else:

            账号 = None if 运营商 == 'sb' else (
                dict(zip(['运营商账号', '运营商密码', '邮箱账号', '邮箱密码'], [i.strip() for i in 账号])))
            info = {}
            info['停止'] = self.启用按钮
            info['异常IMEI处理'] = self.处理异常IMEI
            info['展示'] = self.展示文本
            info['停止变量'] = self.停止变量
            info['账号'] = 账号
            info['进度条'] = self.进度条.SetValue
            info['进度条对象'] = self.进度条
            info['运营商'] = 运营商
            info['imei位置'] = self.imei位置
            info['imei保存位置'] = self.imei保存位置
            info['imei列表'] = self.imei列表
            info['数量'] = num
            info['邮件'] = 邮件
            info['邮件密码'] = 邮件密码
            info['异常通知'] = 异常通知
            info['do接收解锁码邮箱'] = do接收解锁码邮箱
            self.展示文本('******将开始解锁%s个******' % num)
            if 'sb' in 运营商 and time.strftime("%H") >= '20':
                self.展示文本('sb解锁8点后可能停止解锁-若没有解锁码请第二天再试.', 1)
                wx.MessageBox('sb解锁8点后可能停止解锁\n若没有解锁码请第二天再试.', caption='时间提醒')
            if 'au' in 运营商 and time.strftime("%H") >= '20':
                self.展示文本('au解锁晚8.30后将延迟到第二天8.30依次出码', 1)
            self.停止变量[0] = 0
            self.禁用按钮()
            self.进度条.SetRange(num)
            imei2 = self.imei列表.copy()
            for i in imei2:
                if i.startswith('0044'):
                    self.imei列表.remove(i)
                    write_imei(i + ' 工程机\n', info['imei保存位置'], info['imei位置'], self.imei列表)

            # 保存解锁信息供动态插入使用
            self.current_unlock_info = info

            self.t = Thread(target=self.run, args=(运营商, info), name=运营商)
            self.t.daemon = True
            self.t.start()

    def docomo(self):
        if 平台选择 != '电脑':
            self.展示文本('do解锁正在执行-不要断开设备')
            output = subprocess.check_output([adb, "devices"]).decode().strip()
            # 检查输出中是否存在设备
            if self.账号选择 not in output:
                self.展示文本(f'此{self.账号选择}设备离线，请重新切换do选项检查先选au再点do', 1)
                return False
            self.展示文本('初始化中！..')
        driver = login(self.账号选择, self.展示文本)
        # print(driver)  # 0是driver 1 wait
        if isinstance(driver, list):
            message = f"您的Chrome浏览器版本 - {driver[1]}，与的ChromeDriver驱动版本{driver[0]}不兼容。请下载与您的Chrome浏览器版本匹配的ChromeDriver版本。或者请尝试更新chrome app\n再以下网址下载后放入当前解锁程序目录下tool目录替换\nhttps://googlechromelabs.github.io/chrome-for-testing/\n或者 https://developer.chrome.com/docs/chromedriver/downloads"
            self.展示文本(message, 1)
            return False
        if driver == 2:
            return False
        if driver != 1:
            self.展示文本('do解锁正在执行。。。')
            return driver
        else:
            self.展示文本('初始化失败', 1)
            return False

    def run(self, 运营商, info):
        if 运营商 == 'do':
            driver = self.docomo()
            if driver:
                info['账号'] = [self.账号选择, driver]
            else:
                self.展示文本(f'已停止', 1)
                self.停止变量[0] = 1
                self.启用按钮()
                return
        count = __import__(运营商)
        if hasattr(count, 运营商):
            self.展示文本('*********正在执行*********')
            unlock = getattr(count, 运营商)
            上次 = self.停止变量[1].get(运营商)
            if 上次:
                # sb运营商没有账号信息，跳过账号检查
                if 运营商 == 'sb' or 上次 == info['账号']['运营商账号']:
                    unlock(info, 1)
                else:
                    self.展示文本(f'检查到你中途更换了账号!,不支持中途更换,请选择刚才的账号-{上次} 使用', 1)
                    self.停止变量[0] = 1
                    self.启用按钮()
                    return
            else:
                unlock(info)
        else:
            self.展示文本('模块未加载成功!', 1)

    def 出码多开(self, event):
        # 窗口重命名对话框
        dlg = wx.TextEntryDialog(self, "请输入新窗口名称:", "多开窗口重命名", "多开-1")
        if dlg.ShowModal() != wx.ID_OK:
            dlg.Destroy()
            return

        window_name = dlg.GetValue().strip()
        dlg.Destroy()

        if not window_name:
            wx.MessageBox("窗口名称不能为空", "提示", wx.OK | wx.ICON_WARNING)
            return

        用途 = f"""
               DO--目前还不支持多开\n
               AU--需要选择不同的账号名\n
               待解imei位置和已解位置需和之前不同\n
               新窗口名称: {window_name}
               """
        resp = wx.MessageBox(
            用途, '多开解码程序温馨提示',
            wx.OK | wx.CANCEL | wx.ICON_WARNING)
        if resp == wx.OK:
            try:
                # 根据文件类型选择启动方式
                if self.程序路径.lower().endswith('.py'):
                    # Python脚本需要通过解释器启动，传递窗口名称
                    subprocess.Popen([sys.executable, self.程序路径, '1', window_name])
                else:
                    # exe文件可以直接启动，传递窗口名称
                    subprocess.Popen([self.程序路径, '1', window_name])
            except Exception as e:
                print(f'多开打开错误 - {e}')
        else:
            self.展示文本('你取消了多开', 1)
            return


class myApp(wx.App):
    def OnInit(self):
        self.frame = Frame()
        self.frame.Show(True)
        return True


if __name__ == '__main__':
    #
    # try:
    #     ctypes.windll.shcore.SetProcessDpiAwareness(1)
    #     print("DPI感知设置成功")
    # except Exception as e:
    #     print(f"DPI设置失败: {str(e)}")
    app = myApp()
    try:
        app.MainLoop()
    finally:
        # 确保程序退出时清理socket资源
        if hasattr(app, 'frame') and hasattr(app.frame, '_instance_socket') and app.frame._instance_socket:
            try:
                app.frame._instance_socket.close()
            except:
                pass
